import axios from 'axios'
import * as JSONBigNumber from 'json-bignumber'

const DOUYIN_API_BASE_URL = 'https://open.douyin.com/api'

function transformResponse(data: any) {
  try {
    return JSONBigNumber.parse(data)
  } catch (err) {
    console.error(err)
    return data
  }
}

const instance = axios.create({
  baseURL: DOUYIN_API_BASE_URL,
  timeout: 30000,
  transformResponse: [transformResponse],
  headers: {
    'Content-Type': 'application/json',
  },
})

export default instance
