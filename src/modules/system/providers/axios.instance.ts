import axios from 'axios'
import * as JSONBigNumber from 'json-bignumber'

const DOUYIN_API_BASE_URL = 'https://open.douyin.com/api'

function transformResponse(data: any) {
  try {
    return JSONBigNumber.parse(data)
  } catch (err) {
    console.error(err)
    return data
  }
}

const instance = axios.create({
  baseURL: DOUYIN_API_BASE_URL,
  timeout: 30000,
  transformResponse: [transformResponse],
  headers: {
    'Content-Type': 'application/json',
  },
})

// 添加请求拦截器
instance.interceptors.request.use(
  (config) => {
    console.log('🚀 Request Config:', {
      url: config.url,
      method: config.method,
      baseURL: config.baseURL,
      headers: config.headers,
      data: config.data,
      params: config.params,
    })
    return config
  },
  (error) => {
    console.error('❌ Request Error:', error)
    return Promise.reject(error)
  },
)

// 添加响应拦截器
instance.interceptors.response.use(
  (response) => {
    console.log('✅ Response:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data,
    })
    return response
  },
  (error) => {
    console.error('❌ Response Error:', error.response?.data || error.message)
    return Promise.reject(error)
  },
)

export default instance
